const rateLimit = require('express-rate-limit');

/**
 * Rate limiter for authentication endpoints
 * Very strict to prevent brute force attacks
 */
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window per IP
  message: {
    error: 'Too many authentication attempts',
    code: 'RATE_LIMIT_AUTH',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true, // Don't count successful requests
  keyGenerator: (req) => {
    // Use IP + email for more granular limiting
    const email = req.body?.email || '';
    return `${req.ip}:${email}`;
  }
});

/**
 * Rate limiter for password reset endpoints
 * Prevent abuse of password reset functionality
 */
const passwordResetLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // 3 password reset attempts per hour
  message: {
    error: 'Too many password reset attempts',
    code: 'RATE_LIMIT_PASSWORD_RESET',
    retryAfter: '1 hour'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    const email = req.body?.email || '';
    return `password_reset:${req.ip}:${email}`;
  }
});

/**
 * Rate limiter for token refresh endpoints
 * Prevent token refresh abuse
 */
const refreshLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 10, // 10 refresh attempts per 5 minutes
  message: {
    error: 'Too many token refresh attempts',
    code: 'RATE_LIMIT_REFRESH',
    retryAfter: '5 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // Use refresh token hash for more specific limiting
    const refreshToken = req.body?.refreshToken || '';
    const tokenHash = require('crypto').createHash('sha256').update(refreshToken).digest('hex');
    return `refresh:${req.ip}:${tokenHash.substring(0, 16)}`;
  }
});

/**
 * General API rate limiter
 * Moderate limits for general API usage
 */
const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // 100 requests per window per IP
  message: {
    error: 'Too many requests',
    code: 'RATE_LIMIT_GENERAL',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // Skip rate limiting for health checks and static files
    return req.path === '/health' || req.path.startsWith('/public/');
  }
});

/**
 * Strict rate limiter for sensitive operations
 * Very low limits for critical endpoints
 */
const strictLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5, // 5 requests per hour
  message: {
    error: 'Too many requests for sensitive operation',
    code: 'RATE_LIMIT_STRICT',
    retryAfter: '1 hour'
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * Rate limiter for Facebook API endpoints
 * Respect Facebook's rate limits
 */
const facebookLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 200, // 200 requests per hour (conservative)
  message: {
    error: 'Facebook API rate limit exceeded',
    code: 'RATE_LIMIT_FACEBOOK',
    retryAfter: '1 hour'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // Use user ID for per-user Facebook rate limiting
    const userId = req.user?.id || req.ip;
    return `facebook:${userId}`;
  }
});

/**
 * Rate limiter for registration endpoints
 * Prevent spam registrations
 */
const registrationLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // 3 registrations per hour per IP
  message: {
    error: 'Too many registration attempts',
    code: 'RATE_LIMIT_REGISTRATION',
    retryAfter: '1 hour'
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * Custom rate limiter that tracks failed attempts
 */
const createFailureTrackingLimiter = (options) => {
  const failureStore = new Map();
  
  return rateLimit({
    ...options,
    skip: (req) => {
      const key = options.keyGenerator ? options.keyGenerator(req) : req.ip;
      const failures = failureStore.get(key) || 0;
      
      // If too many failures, don't skip (apply rate limit)
      return failures < (options.failureThreshold || 3);
    },
    onLimitReached: (req) => {
      const key = options.keyGenerator ? options.keyGenerator(req) : req.ip;
      const failures = failureStore.get(key) || 0;
      failureStore.set(key, failures + 1);
      
      // Clean up old entries periodically
      if (Math.random() < 0.01) { // 1% chance
        const now = Date.now();
        for (const [k, v] of failureStore.entries()) {
          if (now - v.timestamp > options.windowMs) {
            failureStore.delete(k);
          }
        }
      }
    }
  });
};

module.exports = {
  authLimiter,
  passwordResetLimiter,
  refreshLimiter,
  generalLimiter,
  strictLimiter,
  facebookLimiter,
  registrationLimiter,
  createFailureTrackingLimiter
};
