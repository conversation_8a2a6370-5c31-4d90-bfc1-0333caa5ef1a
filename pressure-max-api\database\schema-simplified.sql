-- Simplified Pressure Max Authentication Schema for Supabase
-- This leverages Supabase Auth instead of custom JWT management

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- User profiles table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS public.user_profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  first_name <PERSON><PERSON><PERSON><PERSON>(100),
  last_name <PERSON><PERSON><PERSON><PERSON>(100),
  role VARCHAR(50) DEFAULT 'user' CHECK (role IN ('user', 'admin', 'moderator')),
  
  -- Facebook integration
  facebook_id VARCHAR(100) UNIQUE,
  facebook_access_token TEXT,
  facebook_token_expires_at TIMESTAMP WITH TIME ZONE,
  facebook_token_valid BOOLEAN DEFAULT true,
  
  -- Account status
  is_active BOOLEAN DEFAULT true,
  
  -- Timestamps
  last_login TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Security events table for audit logging
CREATE TABLE IF NOT EXISTS public.security_events (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  event_type VARCHAR(50) NOT NULL,
  event_data JSONB,
  
  -- Request metadata
  ip_address INET,
  user_agent TEXT,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraint for valid event types
  CHECK (event_type IN (
    'registration_success', 'registration_failure',
    'login_success', 'login_failure', 'logout',
    'facebook_connected', 'facebook_disconnected', 'facebook_token_invalidated',
    'profile_updated', 'password_changed', 'email_changed',
    'suspicious_activity', 'account_locked', 'account_unlocked'
  ))
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON public.user_profiles(email);
CREATE INDEX IF NOT EXISTS idx_user_profiles_facebook_id ON public.user_profiles(facebook_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_role ON public.user_profiles(role);
CREATE INDEX IF NOT EXISTS idx_user_profiles_active ON public.user_profiles(is_active);
CREATE INDEX IF NOT EXISTS idx_user_profiles_facebook_token_valid ON public.user_profiles(facebook_token_valid);

CREATE INDEX IF NOT EXISTS idx_security_events_user_id ON public.security_events(user_id);
CREATE INDEX IF NOT EXISTS idx_security_events_type ON public.security_events(event_type);
CREATE INDEX IF NOT EXISTS idx_security_events_created_at ON public.security_events(created_at);
CREATE INDEX IF NOT EXISTS idx_security_events_ip ON public.security_events(ip_address);

-- Row Level Security (RLS) Policies
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.security_events ENABLE ROW LEVEL SECURITY;

-- User profiles policies
CREATE POLICY "Users can view own profile" ON public.user_profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.user_profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Service role can manage all profiles" ON public.user_profiles
  FOR ALL USING (auth.role() = 'service_role');

-- Security events policies (read-only for users)
CREATE POLICY "Users can view own security events" ON public.security_events
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage all security events" ON public.security_events
  FOR ALL USING (auth.role() = 'service_role');

-- Function for automatic timestamp updates
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for updated_at on user_profiles
CREATE TRIGGER handle_user_profiles_updated_at
  BEFORE UPDATE ON public.user_profiles
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- Function to create user profile on auth user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_profiles (id, email, first_name, last_name)
  VALUES (
    NEW.id,
    NEW.email,
    NEW.raw_user_meta_data->>'first_name',
    NEW.raw_user_meta_data->>'last_name'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create profile on user signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to clean up old security events
CREATE OR REPLACE FUNCTION public.cleanup_old_security_events()
RETURNS void AS $$
BEGIN
  -- Clean up old security events (keep 90 days)
  DELETE FROM public.security_events
  WHERE created_at < NOW() - INTERVAL '90 days';
  
  -- Log cleanup event
  INSERT INTO public.security_events (event_type, event_data)
  VALUES ('system_cleanup', jsonb_build_object('action', 'security_events_cleanup', 'timestamp', NOW()));
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check Facebook token expiry
CREATE OR REPLACE FUNCTION public.check_facebook_token_expiry(user_uuid UUID)
RETURNS TABLE (
  needs_refresh BOOLEAN,
  days_until_expiry INTEGER,
  reason TEXT
) AS $$
DECLARE
  profile_record RECORD;
  days_diff INTEGER;
BEGIN
  SELECT facebook_token_expires_at, facebook_token_valid
  INTO profile_record
  FROM public.user_profiles
  WHERE id = user_uuid;
  
  IF NOT FOUND THEN
    RETURN QUERY SELECT true, 0, 'user_not_found';
    RETURN;
  END IF;
  
  IF profile_record.facebook_token_expires_at IS NULL THEN
    RETURN QUERY SELECT true, 0, 'no_expiry_date';
    RETURN;
  END IF;
  
  IF NOT profile_record.facebook_token_valid THEN
    RETURN QUERY SELECT true, 0, 'token_invalid';
    RETURN;
  END IF;
  
  days_diff := EXTRACT(DAY FROM profile_record.facebook_token_expires_at - NOW());
  
  IF days_diff < 7 THEN
    RETURN QUERY SELECT true, days_diff, 'expiring_soon';
  ELSE
    RETURN QUERY SELECT false, days_diff, 'token_valid';
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.user_profiles TO authenticated;
GRANT SELECT, INSERT ON public.security_events TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION public.check_facebook_token_expiry(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.cleanup_old_security_events() TO service_role;

-- Create a view for user dashboard data
CREATE OR REPLACE VIEW public.user_dashboard_data AS
SELECT 
  up.id,
  up.email,
  up.first_name,
  up.last_name,
  up.role,
  up.facebook_id IS NOT NULL as facebook_connected,
  up.facebook_token_valid,
  up.last_login,
  up.created_at,
  -- Count of security events in last 30 days
  (SELECT COUNT(*) FROM public.security_events se 
   WHERE se.user_id = up.id 
   AND se.created_at > NOW() - INTERVAL '30 days') as recent_activity_count
FROM public.user_profiles up
WHERE up.is_active = true;

-- Grant access to the view
GRANT SELECT ON public.user_dashboard_data TO authenticated;

-- RLS policy for the view
CREATE POLICY "Users can view own dashboard data" ON public.user_dashboard_data
  FOR SELECT USING (auth.uid() = id);

-- Comments for documentation
COMMENT ON TABLE public.user_profiles IS 'Extended user profiles with Facebook integration - simplified version using Supabase Auth';
COMMENT ON TABLE public.security_events IS 'Audit log for security-related events';
COMMENT ON FUNCTION public.check_facebook_token_expiry(UUID) IS 'Check if Facebook token needs refresh';
COMMENT ON VIEW public.user_dashboard_data IS 'Consolidated user data for dashboard display';

-- Insert initial admin user (optional - run after creating your first user)
-- UPDATE public.user_profiles SET role = 'admin' WHERE email = '<EMAIL>';
