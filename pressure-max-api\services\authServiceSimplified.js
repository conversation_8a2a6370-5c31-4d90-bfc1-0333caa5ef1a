const { supabase, supabaseAdmin } = require('../config/supabase');
const axios = require('axios');

class AuthService {
  /**
   * Register a new user using Supabase Auth
   */
  static async register(userData) {
    const { email, password, firstName, lastName } = userData;

    try {
      // Use Supabase's built-in registration
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: firstName,
            last_name: lastName
          }
        }
      });

      if (error) {
        throw new Error(error.message);
      }

      // Log security event
      await this.logSecurityEvent(data.user?.id, 'registration_success', {
        email,
        method: 'email_password'
      });

      return {
        user: data.user,
        session: data.session,
        message: 'Registration successful. Please check your email for verification.'
      };
    } catch (error) {
      console.error('Registration error:', error);
      
      // Log failed registration
      await this.logSecurityEvent(null, 'registration_failure', {
        email,
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Login user using Supabase Auth
   */
  static async login(email, password, deviceInfo = {}) {
    try {
      // Use Supabase's built-in login
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        // Log failed login
        await this.logSecurityEvent(null, 'login_failure', {
          email,
          error: error.message,
          ...deviceInfo
        });
        throw new Error(error.message);
      }

      // Update last login in user profile
      await supabaseAdmin
        .from('user_profiles')
        .update({ 
          last_login: new Date(),
          updated_at: new Date()
        })
        .eq('id', data.user.id);

      // Get user profile for additional data
      const { data: profile } = await supabaseAdmin
        .from('user_profiles')
        .select('*')
        .eq('id', data.user.id)
        .single();

      // Log successful login
      await this.logSecurityEvent(data.user.id, 'login_success', {
        email,
        ...deviceInfo
      });

      return {
        user: data.user,
        session: data.session,
        profile: profile ? {
          firstName: profile.first_name,
          lastName: profile.last_name,
          role: profile.role,
          facebookConnected: !!profile.facebook_id
        } : null
      };
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  /**
   * Logout user using Supabase Auth
   */
  static async logout(userId = null) {
    try {
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        throw new Error(error.message);
      }

      // Log logout event
      if (userId) {
        await this.logSecurityEvent(userId, 'logout', {
          method: 'manual'
        });
      }

      return { success: true, message: 'Logged out successfully' };
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  }

  /**
   * Get current user from token
   */
  static async getCurrentUser(token) {
    try {
      const { data: { user }, error } = await supabase.auth.getUser(token);
      
      if (error || !user) {
        return null;
      }

      // Get user profile
      const { data: profile } = await supabaseAdmin
        .from('user_profiles')
        .select('*')
        .eq('id', user.id)
        .eq('is_active', true)
        .single();

      if (!profile) {
        return null;
      }

      return {
        id: user.id,
        email: user.email,
        firstName: profile.first_name,
        lastName: profile.last_name,
        role: profile.role,
        facebookConnected: !!profile.facebook_id,
        lastLogin: profile.last_login,
        createdAt: profile.created_at
      };
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  /**
   * Connect Facebook account
   */
  static async connectFacebook(userId, facebookData) {
    try {
      const { access_token, expires_in, user_id, email } = facebookData;
      
      // Calculate token expiration
      const expiresAt = expires_in 
        ? new Date(Date.now() + (expires_in * 1000))
        : new Date(Date.now() + (60 * 24 * 60 * 60 * 1000)); // Default 60 days

      // Update user profile with Facebook data
      const { error } = await supabaseAdmin
        .from('user_profiles')
        .update({
          facebook_id: user_id,
          facebook_access_token: access_token,
          facebook_token_expires_at: expiresAt,
          facebook_token_valid: true,
          updated_at: new Date()
        })
        .eq('id', userId);

      if (error) {
        throw new Error('Failed to connect Facebook account');
      }

      // Log Facebook connection
      await this.logSecurityEvent(userId, 'facebook_connected', {
        facebook_id: user_id,
        facebook_email: email
      });

      return { 
        success: true, 
        message: 'Facebook account connected successfully',
        expiresAt 
      };
    } catch (error) {
      console.error('Facebook connection error:', error);
      throw error;
    }
  }

  /**
   * Disconnect Facebook account
   */
  static async disconnectFacebook(userId) {
    try {
      const { error } = await supabaseAdmin
        .from('user_profiles')
        .update({
          facebook_id: null,
          facebook_access_token: null,
          facebook_token_expires_at: null,
          facebook_token_valid: false,
          updated_at: new Date()
        })
        .eq('id', userId);

      if (error) {
        throw new Error('Failed to disconnect Facebook account');
      }

      // Log Facebook disconnection
      await this.logSecurityEvent(userId, 'facebook_disconnected', {
        method: 'manual'
      });

      return { 
        success: true, 
        message: 'Facebook account disconnected successfully' 
      };
    } catch (error) {
      console.error('Facebook disconnection error:', error);
      throw error;
    }
  }

  /**
   * Invalidate Facebook token when API calls fail
   */
  static async invalidateFacebookToken(userId, reason = 'api_error') {
    try {
      await supabaseAdmin
        .from('user_profiles')
        .update({
          facebook_token_valid: false,
          updated_at: new Date()
        })
        .eq('id', userId);

      // Log token invalidation
      await this.logSecurityEvent(userId, 'facebook_token_invalidated', {
        reason
      });

      console.log(`Facebook token invalidated for user ${userId}: ${reason}`);
    } catch (error) {
      console.error('Error invalidating Facebook token:', error);
    }
  }

  /**
   * Update user profile
   */
  static async updateProfile(userId, updates) {
    try {
      const allowedUpdates = ['first_name', 'last_name'];
      const filteredUpdates = {};
      
      for (const key of allowedUpdates) {
        if (updates[key] !== undefined) {
          filteredUpdates[key] = updates[key];
        }
      }

      if (Object.keys(filteredUpdates).length === 0) {
        throw new Error('No valid updates provided');
      }

      filteredUpdates.updated_at = new Date();

      const { data: user, error } = await supabaseAdmin
        .from('user_profiles')
        .update(filteredUpdates)
        .eq('id', userId)
        .select()
        .single();

      if (error) {
        throw new Error('Failed to update profile');
      }

      return {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        role: user.role,
        facebookConnected: !!user.facebook_id
      };
    } catch (error) {
      console.error('Update profile error:', error);
      throw error;
    }
  }

  /**
   * Log security events
   */
  static async logSecurityEvent(userId, eventType, eventData = {}, req = null) {
    try {
      await supabaseAdmin
        .from('security_events')
        .insert({
          user_id: userId,
          event_type: eventType,
          event_data: eventData,
          ip_address: req?.ip || eventData.ip_address,
          user_agent: req?.headers['user-agent'] || eventData.user_agent,
          created_at: new Date()
        });
    } catch (error) {
      console.error('Error logging security event:', error);
    }
  }

  /**
   * Check if Facebook token needs refresh
   */
  static async checkFacebookTokenExpiry(userId) {
    try {
      const { data: profile } = await supabaseAdmin
        .from('user_profiles')
        .select('facebook_token_expires_at, facebook_token_valid')
        .eq('id', userId)
        .single();

      if (!profile || !profile.facebook_token_expires_at) {
        return { needsRefresh: true, reason: 'no_expiry_date' };
      }

      const expiresAt = new Date(profile.facebook_token_expires_at);
      const now = new Date();
      const daysUntilExpiry = (expiresAt - now) / (1000 * 60 * 60 * 24);

      // Warn if token expires in less than 7 days
      if (daysUntilExpiry < 7) {
        return { 
          needsRefresh: true, 
          reason: 'expiring_soon',
          daysUntilExpiry: Math.floor(daysUntilExpiry)
        };
      }

      // Check if token is marked as invalid
      if (!profile.facebook_token_valid) {
        return { needsRefresh: true, reason: 'token_invalid' };
      }

      return { needsRefresh: false, daysUntilExpiry: Math.floor(daysUntilExpiry) };
    } catch (error) {
      console.error('Error checking Facebook token expiry:', error);
      return { needsRefresh: true, reason: 'check_error' };
    }
  }
}

module.exports = AuthService;
