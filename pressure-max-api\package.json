{"name": "pressure-max-api", "version": "1.0.0", "description": "Production API for Facebook Marketing automation with smart caching and rate limiting", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@supabase/supabase-js": "^2.50.2", "axios": "^1.6.2", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}, "keywords": ["facebook-marketing-api", "campaign-management", "api", "rate-limiting", "caching"], "author": "Pressure Max Team", "license": "MIT"}