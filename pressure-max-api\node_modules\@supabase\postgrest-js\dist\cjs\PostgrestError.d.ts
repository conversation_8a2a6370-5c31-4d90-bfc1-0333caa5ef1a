/**
 * Error format
 *
 * {@link https://postgrest.org/en/stable/api.html?highlight=options#errors-and-http-status-codes}
 */
export default class PostgrestError extends Error {
    details: string;
    hint: string;
    code: string;
    constructor(context: {
        message: string;
        details: string;
        hint: string;
        code: string;
    });
}
//# sourceMappingURL=PostgrestError.d.ts.map