const jwt = require('jsonwebtoken');
const crypto = require('crypto');

class JWTManager {
  /**
   * Generate access token with user payload
   * @param {Object} payload - User data to include in token
   * @returns {string} JWT access token
   */
  static generateAccessToken(payload) {
    const tokenPayload = {
      userId: payload.userId,
      email: payload.email,
      role: payload.role,
      iat: Math.floor(Date.now() / 1000),
      type: 'access'
    };

    return jwt.sign(tokenPayload, process.env.JWT_SECRET, {
      expiresIn: process.env.JWT_EXPIRES_IN || '15m',
      issuer: 'pressure-max-api',
      audience: 'pressure-max-app',
      algorithm: 'HS256'
    });
  }

  /**
   * Generate cryptographically secure refresh token
   * @returns {string} Random refresh token
   */
  static generateRefreshToken() {
    return crypto.randomBytes(64).toString('hex');
  }

  /**
   * Verify and decode access token
   * @param {string} token - JWT token to verify
   * @returns {Object} Decoded token payload
   */
  static verifyAccessToken(token) {
    try {
      return jwt.verify(token, process.env.JWT_SECRET, {
        issuer: 'pressure-max-api',
        audience: 'pressure-max-app',
        algorithms: ['HS256']
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Hash refresh token for secure storage
   * @param {string} token - Refresh token to hash
   * @returns {string} SHA256 hash of token
   */
  static hashRefreshToken(token) {
    return crypto.createHash('sha256').update(token).digest('hex');
  }

  /**
   * Generate device fingerprint from request
   * @param {Object} req - Express request object
   * @returns {Object} Device information
   */
  static generateDeviceFingerprint(req) {
    const userAgent = req.headers['user-agent'] || '';
    const acceptLanguage = req.headers['accept-language'] || '';
    const acceptEncoding = req.headers['accept-encoding'] || '';
    
    return {
      userAgent,
      acceptLanguage,
      acceptEncoding,
      ip: req.ip || req.connection.remoteAddress,
      fingerprint: crypto
        .createHash('sha256')
        .update(userAgent + acceptLanguage + acceptEncoding)
        .digest('hex')
    };
  }

  /**
   * Extract token from Authorization header
   * @param {Object} req - Express request object
   * @returns {string|null} Token or null if not found
   */
  static extractTokenFromHeader(req) {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    return authHeader.substring(7);
  }

  /**
   * Generate secure session token
   * @returns {string} Random session token
   */
  static generateSessionToken() {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Validate token expiration
   * @param {number} exp - Token expiration timestamp
   * @returns {boolean} True if token is still valid
   */
  static isTokenValid(exp) {
    return exp > Math.floor(Date.now() / 1000);
  }

  /**
   * Get token expiration time
   * @param {string} expiresIn - Expiration string (e.g., '15m', '7d')
   * @returns {Date} Expiration date
   */
  static getExpirationDate(expiresIn) {
    const now = new Date();
    const match = expiresIn.match(/^(\d+)([smhd])$/);
    
    if (!match) {
      throw new Error('Invalid expiration format');
    }

    const value = parseInt(match[1]);
    const unit = match[2];

    switch (unit) {
      case 's':
        return new Date(now.getTime() + value * 1000);
      case 'm':
        return new Date(now.getTime() + value * 60 * 1000);
      case 'h':
        return new Date(now.getTime() + value * 60 * 60 * 1000);
      case 'd':
        return new Date(now.getTime() + value * 24 * 60 * 60 * 1000);
      default:
        throw new Error('Invalid time unit');
    }
  }
}

module.exports = JWTManager;
