{"version": 3, "file": "PostgrestQueryBuilder.js", "sourceRoot": "", "sources": ["../../src/PostgrestQueryBuilder.ts"], "names": [], "mappings": ";;;;;AACA,sFAA6D;AAI7D,MAAqB,qBAAqB;IAYxC,YACE,GAAQ,EACR,EACE,OAAO,GAAG,EAAE,EACZ,MAAM,EACN,KAAK,GAKN;QAED,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;QACd,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;IACpB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,MAAM,CAIJ,OAAe,EACf,EACE,IAAI,GAAG,KAAK,EACZ,KAAK,MAIH,EAAE;QAEN,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAA;QACpC,wCAAwC;QACxC,IAAI,MAAM,GAAG,KAAK,CAAA;QAClB,MAAM,cAAc,GAAG,CAAC,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,GAAG,CAAC;aACpC,KAAK,CAAC,EAAE,CAAC;aACT,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YACT,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE;gBAC3B,OAAO,EAAE,CAAA;aACV;YACD,IAAI,CAAC,KAAK,GAAG,EAAE;gBACb,MAAM,GAAG,CAAC,MAAM,CAAA;aACjB;YACD,OAAO,CAAC,CAAA;QACV,CAAC,CAAC;aACD,IAAI,CAAC,EAAE,CAAC,CAAA;QACX,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAA;QACnD,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,SAAS,KAAK,EAAE,CAAA;SAC1C;QAED,OAAO,IAAI,gCAAsB,CAAC;YAChC,MAAM;YACN,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,UAAU,EAAE,KAAK;SAC0B,CAAC,CAAA;IAChD,CAAC;IAgBD;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;IACH,MAAM,CACJ,MAAmB,EACnB,EACE,KAAK,EACL,aAAa,GAAG,IAAI,MAIlB,EAAE;QAEN,MAAM,MAAM,GAAG,MAAM,CAAA;QAErB,MAAM,cAAc,GAAG,EAAE,CAAA;QACzB,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC1B,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAA;SAC5C;QACD,IAAI,KAAK,EAAE;YACT,cAAc,CAAC,IAAI,CAAC,SAAS,KAAK,EAAE,CAAC,CAAA;SACtC;QACD,IAAI,CAAC,aAAa,EAAE;YAClB,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;SACvC;QACD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAEjD,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACzB,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,EAAc,CAAC,CAAA;YACrF,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;gBACtB,MAAM,aAAa,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,GAAG,CAAC,CAAA;gBAC1E,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,EAAE,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;aAC9D;SACF;QAED,OAAO,IAAI,gCAAsB,CAAC;YAChC,MAAM;YACN,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,UAAU,EAAE,KAAK;SACmB,CAAC,CAAA;IACzC,CAAC;IAoBD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAqCG;IACH,MAAM,CACJ,MAAmB,EACnB,EACE,UAAU,EACV,gBAAgB,GAAG,KAAK,EACxB,KAAK,EACL,aAAa,GAAG,IAAI,MAMlB,EAAE;QAEN,MAAM,MAAM,GAAG,MAAM,CAAA;QAErB,MAAM,cAAc,GAAG,CAAC,cAAc,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,aAAa,CAAC,CAAA;QAEzF,IAAI,UAAU,KAAK,SAAS;YAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE,UAAU,CAAC,CAAA;QAClF,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC1B,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAA;SAC5C;QACD,IAAI,KAAK,EAAE;YACT,cAAc,CAAC,IAAI,CAAC,SAAS,KAAK,EAAE,CAAC,CAAA;SACtC;QACD,IAAI,CAAC,aAAa,EAAE;YAClB,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;SACvC;QACD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAEjD,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACzB,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,EAAc,CAAC,CAAA;YACrF,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;gBACtB,MAAM,aAAa,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,GAAG,CAAC,CAAA;gBAC1E,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,EAAE,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;aAC9D;SACF;QAED,OAAO,IAAI,gCAAsB,CAAC;YAChC,MAAM;YACN,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,UAAU,EAAE,KAAK;SACmB,CAAC,CAAA;IACzC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,MAAM,CACJ,MAAW,EACX,EACE,KAAK,MAGH,EAAE;QAEN,MAAM,MAAM,GAAG,OAAO,CAAA;QACtB,MAAM,cAAc,GAAG,EAAE,CAAA;QACzB,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC1B,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAA;SAC5C;QACD,IAAI,KAAK,EAAE;YACT,cAAc,CAAC,IAAI,CAAC,SAAS,KAAK,EAAE,CAAC,CAAA;SACtC;QACD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAEjD,OAAO,IAAI,gCAAsB,CAAC;YAChC,MAAM;YACN,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,UAAU,EAAE,KAAK;SACmB,CAAC,CAAA;IACzC,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACH,MAAM,CAAC,EACL,KAAK,MAGH,EAAE;QACJ,MAAM,MAAM,GAAG,QAAQ,CAAA;QACvB,MAAM,cAAc,GAAG,EAAE,CAAA;QACzB,IAAI,KAAK,EAAE;YACT,cAAc,CAAC,IAAI,CAAC,SAAS,KAAK,EAAE,CAAC,CAAA;SACtC;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC1B,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAA;SAC/C;QACD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAEjD,OAAO,IAAI,gCAAsB,CAAC;YAChC,MAAM;YACN,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,UAAU,EAAE,KAAK;SACmB,CAAC,CAAA;IACzC,CAAC;CACF;AAvXD,wCAuXC"}