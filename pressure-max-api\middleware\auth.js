const { supabase } = require('../config/supabase');
const JWTManager = require('../utils/jwt');

/**
 * Middleware to authenticate JWT tokens
 */
const authenticateToken = async (req, res, next) => {
  try {
    const token = JWTManager.extractTokenFromHeader(req);

    if (!token) {
      return res.status(401).json({ 
        error: 'Access token required',
        code: 'TOKEN_MISSING'
      });
    }

    // Verify JWT token
    const decoded = JWTManager.verifyAccessToken(token);
    
    // Check if token type is correct
    if (decoded.type !== 'access') {
      return res.status(401).json({ 
        error: 'Invalid token type',
        code: 'INVALID_TOKEN_TYPE'
      });
    }

    // Verify user still exists and is active
    const { data: user, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', decoded.userId)
      .eq('is_active', true)
      .single();

    if (error || !user) {
      return res.status(401).json({ 
        error: 'User not found or inactive',
        code: 'USER_NOT_FOUND'
      });
    }

    // Update last activity
    await supabase
      .from('user_profiles')
      .update({ last_login: new Date() })
      .eq('id', user.id);

    // Attach user to request
    req.user = user;
    req.tokenPayload = decoded;
    
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ 
        error: 'Token expired',
        code: 'TOKEN_EXPIRED'
      });
    }
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(403).json({ 
        error: 'Invalid token',
        code: 'INVALID_TOKEN'
      });
    }
    
    return res.status(500).json({ 
      error: 'Authentication failed',
      code: 'AUTH_ERROR'
    });
  }
};

/**
 * Middleware to require specific roles
 */
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ 
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    const userRoles = Array.isArray(req.user.role) ? req.user.role : [req.user.role];
    const requiredRoles = Array.isArray(roles) ? roles : [roles];

    const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role));

    if (!hasRequiredRole) {
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        code: 'INSUFFICIENT_PERMISSIONS',
        required: requiredRoles,
        current: userRoles
      });
    }

    next();
  };
};

/**
 * Middleware for optional authentication (doesn't fail if no token)
 */
const optionalAuth = async (req, res, next) => {
  try {
    const token = JWTManager.extractTokenFromHeader(req);

    if (!token) {
      return next(); // Continue without authentication
    }

    const decoded = JWTManager.verifyAccessToken(token);
    
    if (decoded.type === 'access') {
      const { data: user, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', decoded.userId)
        .eq('is_active', true)
        .single();

      if (!error && user) {
        req.user = user;
        req.tokenPayload = decoded;
      }
    }
  } catch (error) {
    // Silently fail for optional auth
    console.log('Optional auth failed:', error.message);
  }
  
  next();
};

/**
 * Middleware to validate refresh token
 */
const validateRefreshToken = async (req, res, next) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({ 
        error: 'Refresh token required',
        code: 'REFRESH_TOKEN_MISSING'
      });
    }

    const tokenHash = JWTManager.hashRefreshToken(refreshToken);

    // Check if refresh token exists and is valid
    const { data: tokenRecord, error } = await supabase
      .from('refresh_tokens')
      .select('*')
      .eq('token_hash', tokenHash)
      .is('revoked_at', null)
      .gt('expires_at', new Date().toISOString())
      .single();

    if (error || !tokenRecord) {
      return res.status(401).json({ 
        error: 'Invalid or expired refresh token',
        code: 'INVALID_REFRESH_TOKEN'
      });
    }

    // Get user associated with token
    const { data: user, error: userError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', tokenRecord.user_id)
      .eq('is_active', true)
      .single();

    if (userError || !user) {
      return res.status(401).json({ 
        error: 'User not found or inactive',
        code: 'USER_NOT_FOUND'
      });
    }

    req.user = user;
    req.refreshTokenRecord = tokenRecord;
    req.refreshToken = refreshToken;
    
    next();
  } catch (error) {
    console.error('Refresh token validation error:', error);
    return res.status(500).json({ 
      error: 'Token validation failed',
      code: 'VALIDATION_ERROR'
    });
  }
};

/**
 * Middleware to check if user has Facebook connection
 */
const requireFacebookAuth = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({ 
      error: 'Authentication required',
      code: 'AUTH_REQUIRED'
    });
  }

  if (!req.user.facebook_id || !req.user.facebook_access_token) {
    return res.status(403).json({ 
      error: 'Facebook account connection required',
      code: 'FACEBOOK_AUTH_REQUIRED'
    });
  }

  next();
};

module.exports = {
  authenticateToken,
  requireRole,
  optionalAuth,
  validateRefreshToken,
  requireFacebookAuth
};
