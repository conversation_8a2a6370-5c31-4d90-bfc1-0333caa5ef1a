const { supabase, supabaseAdmin } = require('../config/supabase');

/**
 * Simplified authentication middleware using Supabase Auth
 */
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.startsWith('Bearer ') 
      ? authHeader.substring(7) 
      : null;

    if (!token) {
      return res.status(401).json({ 
        error: 'Access token required',
        code: 'TOKEN_MISSING'
      });
    }

    // Use Supabase's built-in token verification
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      return res.status(401).json({ 
        error: 'Invalid or expired token',
        code: 'INVALID_TOKEN'
      });
    }

    // Get user profile for additional info and status check
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('user_profiles')
      .select('*')
      .eq('id', user.id)
      .eq('is_active', true)
      .single();

    if (profileError || !profile) {
      return res.status(401).json({ 
        error: 'User not found or inactive',
        code: 'USER_INACTIVE'
      });
    }

    // Update last login timestamp
    await supabaseAdmin
      .from('user_profiles')
      .update({ last_login: new Date() })
      .eq('id', user.id);

    // Attach user data to request
    req.user = {
      id: user.id,
      email: user.email,
      firstName: profile.first_name,
      lastName: profile.last_name,
      role: profile.role,
      facebookId: profile.facebook_id,
      facebookAccessToken: profile.facebook_access_token,
      facebookTokenValid: profile.facebook_token_valid,
      isActive: profile.is_active,
      lastLogin: profile.last_login
    };
    
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(500).json({ 
      error: 'Authentication failed',
      code: 'AUTH_ERROR'
    });
  }
};

/**
 * Middleware to require specific roles
 */
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ 
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    const userRoles = Array.isArray(req.user.role) ? req.user.role : [req.user.role];
    const requiredRoles = Array.isArray(roles) ? roles : [roles];

    const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role));

    if (!hasRequiredRole) {
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        code: 'INSUFFICIENT_PERMISSIONS',
        required: requiredRoles,
        current: userRoles
      });
    }

    next();
  };
};

/**
 * Middleware for optional authentication (doesn't fail if no token)
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.startsWith('Bearer ') 
      ? authHeader.substring(7) 
      : null;

    if (!token) {
      return next(); // Continue without authentication
    }

    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (!error && user) {
      const { data: profile } = await supabaseAdmin
        .from('user_profiles')
        .select('*')
        .eq('id', user.id)
        .eq('is_active', true)
        .single();

      if (profile) {
        req.user = {
          id: user.id,
          email: user.email,
          firstName: profile.first_name,
          lastName: profile.last_name,
          role: profile.role,
          facebookId: profile.facebook_id,
          facebookAccessToken: profile.facebook_access_token,
          facebookTokenValid: profile.facebook_token_valid
        };
      }
    }
  } catch (error) {
    // Silently fail for optional auth
    console.log('Optional auth failed:', error.message);
  }
  
  next();
};

/**
 * Middleware to check if user has valid Facebook connection
 */
const requireValidFacebookToken = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({ 
      error: 'Authentication required',
      code: 'AUTH_REQUIRED'
    });
  }

  if (!req.user.facebookId || !req.user.facebookAccessToken || !req.user.facebookTokenValid) {
    return res.status(403).json({ 
      error: 'Facebook account connection required',
      code: 'FACEBOOK_TOKEN_INVALID',
      message: 'Please reconnect your Facebook account'
    });
  }

  next();
};

/**
 * Function to invalidate Facebook token when API calls fail
 */
const invalidateFacebookToken = async (userId) => {
  try {
    await supabaseAdmin
      .from('user_profiles')
      .update({ 
        facebook_token_valid: false,
        updated_at: new Date()
      })
      .eq('id', userId);
    
    console.log(`Facebook token invalidated for user: ${userId}`);
  } catch (error) {
    console.error('Error invalidating Facebook token:', error);
  }
};

/**
 * Middleware to log security events
 */
const logSecurityEvent = (eventType) => {
  return async (req, res, next) => {
    try {
      const eventData = {
        ip_address: req.ip,
        user_agent: req.headers['user-agent'],
        endpoint: req.path,
        method: req.method
      };

      await supabaseAdmin
        .from('security_events')
        .insert({
          user_id: req.user?.id || null,
          event_type: eventType,
          event_data: eventData,
          ip_address: req.ip,
          user_agent: req.headers['user-agent'],
          created_at: new Date()
        });
    } catch (error) {
      console.error('Error logging security event:', error);
    }
    
    next();
  };
};

module.exports = {
  authenticateToken,
  requireRole,
  optionalAuth,
  requireValidFacebookToken,
  invalidateFacebookToken,
  logSecurityEvent
};
