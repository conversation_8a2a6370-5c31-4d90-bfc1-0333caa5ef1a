const bcrypt = require('bcryptjs');
const { supabase, supabaseAuth } = require('../config/supabase');
const JWTManager = require('../utils/jwt');

class AuthService {
  /**
   * Register a new user
   */
  static async register(userData) {
    const { email, password, firstName, lastName } = userData;

    try {
      // Check if user already exists
      const { data: existingUser } = await supabase
        .from('user_profiles')
        .select('id')
        .eq('email', email)
        .single();

      if (existingUser) {
        throw new Error('User already exists with this email');
      }

      // Create user in Supabase Auth
      const { data: authData, error: authError } = await supabaseAuth.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: firstName,
            last_name: lastName
          }
        }
      });

      if (authError) {
        throw new Error(authError.message);
      }

      // Create user profile
      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .insert({
          id: authData.user.id,
          email,
          first_name: firstName,
          last_name: lastName,
          role: 'user',
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        })
        .select()
        .single();

      if (profileError) {
        // Clean up auth user if profile creation fails
        await supabaseAuth.auth.admin.deleteUser(authData.user.id);
        throw new Error('Failed to create user profile');
      }

      return {
        user: {
          id: profile.id,
          email: profile.email,
          firstName: profile.first_name,
          lastName: profile.last_name,
          role: profile.role
        },
        authUser: authData.user
      };
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  }

  /**
   * Login user with email and password
   */
  static async login(email, password, deviceInfo = {}) {
    try {
      // Authenticate with Supabase
      const { data: authData, error: authError } = await supabaseAuth.auth.signInWithPassword({
        email,
        password
      });

      if (authError) {
        throw new Error('Invalid email or password');
      }

      // Get user profile
      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', authData.user.id)
        .eq('is_active', true)
        .single();

      if (profileError || !profile) {
        throw new Error('User profile not found or inactive');
      }

      // Update last login
      await supabase
        .from('user_profiles')
        .update({ 
          last_login: new Date(),
          updated_at: new Date()
        })
        .eq('id', profile.id);

      // Generate tokens
      const tokens = await this.generateTokens(profile, deviceInfo);

      return {
        user: {
          id: profile.id,
          email: profile.email,
          firstName: profile.first_name,
          lastName: profile.last_name,
          role: profile.role,
          facebookConnected: !!profile.facebook_id
        },
        tokens
      };
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  /**
   * Generate access and refresh tokens
   */
  static async generateTokens(user, deviceInfo = {}) {
    try {
      // Generate access token
      const accessToken = JWTManager.generateAccessToken({
        userId: user.id,
        email: user.email,
        role: user.role
      });

      // Generate refresh token
      const refreshToken = JWTManager.generateRefreshToken();
      const refreshTokenHash = JWTManager.hashRefreshToken(refreshToken);

      // Store refresh token in database
      const expiresAt = JWTManager.getExpirationDate(
        process.env.JWT_REFRESH_EXPIRES_IN || '30d'
      );

      await supabase.from('refresh_tokens').insert({
        user_id: user.id,
        token_hash: refreshTokenHash,
        device_info: deviceInfo,
        ip_address: deviceInfo.ip,
        expires_at: expiresAt,
        created_at: new Date()
      });

      return {
        accessToken,
        refreshToken,
        expiresIn: process.env.JWT_EXPIRES_IN || '15m'
      };
    } catch (error) {
      console.error('Token generation error:', error);
      throw new Error('Failed to generate tokens');
    }
  }

  /**
   * Refresh access token using refresh token
   */
  static async refreshToken(refreshToken, deviceInfo = {}) {
    try {
      const refreshTokenHash = JWTManager.hashRefreshToken(refreshToken);

      // Find and validate refresh token
      const { data: tokenRecord, error } = await supabase
        .from('refresh_tokens')
        .select('*')
        .eq('token_hash', refreshTokenHash)
        .is('revoked_at', null)
        .gt('expires_at', new Date().toISOString())
        .single();

      if (error || !tokenRecord) {
        throw new Error('Invalid or expired refresh token');
      }

      // Get user
      const { data: user, error: userError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', tokenRecord.user_id)
        .eq('is_active', true)
        .single();

      if (userError || !user) {
        throw new Error('User not found or inactive');
      }

      // Revoke old refresh token
      await supabase
        .from('refresh_tokens')
        .update({ revoked_at: new Date() })
        .eq('id', tokenRecord.id);

      // Generate new tokens
      const tokens = await this.generateTokens(user, deviceInfo);

      return {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          role: user.role,
          facebookConnected: !!user.facebook_id
        },
        tokens
      };
    } catch (error) {
      console.error('Token refresh error:', error);
      throw error;
    }
  }

  /**
   * Logout user and revoke tokens
   */
  static async logout(userId, refreshToken = null) {
    try {
      // Revoke all refresh tokens for user
      await supabase
        .from('refresh_tokens')
        .update({ revoked_at: new Date() })
        .eq('user_id', userId)
        .is('revoked_at', null);

      // If specific refresh token provided, revoke it
      if (refreshToken) {
        const refreshTokenHash = JWTManager.hashRefreshToken(refreshToken);
        await supabase
          .from('refresh_tokens')
          .update({ revoked_at: new Date() })
          .eq('token_hash', refreshTokenHash);
      }

      // Revoke all sessions
      await supabase
        .from('user_sessions')
        .delete()
        .eq('user_id', userId);

      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);
      throw new Error('Failed to logout');
    }
  }

  /**
   * Validate user session
   */
  static async validateSession(userId, sessionToken) {
    try {
      const { data: session, error } = await supabase
        .from('user_sessions')
        .select('*')
        .eq('user_id', userId)
        .eq('session_token', sessionToken)
        .gt('expires_at', new Date().toISOString())
        .single();

      if (error || !session) {
        return false;
      }

      // Update last activity
      await supabase
        .from('user_sessions')
        .update({ last_activity: new Date() })
        .eq('id', session.id);

      return true;
    } catch (error) {
      console.error('Session validation error:', error);
      return false;
    }
  }

  /**
   * Get user by ID
   */
  static async getUserById(userId) {
    try {
      const { data: user, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .eq('is_active', true)
        .single();

      if (error || !user) {
        return null;
      }

      return {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        role: user.role,
        facebookConnected: !!user.facebook_id,
        lastLogin: user.last_login,
        createdAt: user.created_at
      };
    } catch (error) {
      console.error('Get user error:', error);
      return null;
    }
  }

  /**
   * Update user profile
   */
  static async updateProfile(userId, updates) {
    try {
      const allowedUpdates = ['first_name', 'last_name'];
      const filteredUpdates = {};
      
      for (const key of allowedUpdates) {
        if (updates[key] !== undefined) {
          filteredUpdates[key] = updates[key];
        }
      }

      if (Object.keys(filteredUpdates).length === 0) {
        throw new Error('No valid updates provided');
      }

      filteredUpdates.updated_at = new Date();

      const { data: user, error } = await supabase
        .from('user_profiles')
        .update(filteredUpdates)
        .eq('id', userId)
        .select()
        .single();

      if (error) {
        throw new Error('Failed to update profile');
      }

      return {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        role: user.role,
        facebookConnected: !!user.facebook_id
      };
    } catch (error) {
      console.error('Update profile error:', error);
      throw error;
    }
  }
}

module.exports = AuthService;
